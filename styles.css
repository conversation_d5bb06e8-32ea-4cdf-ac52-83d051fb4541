/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    margin-bottom: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px 0;
    border-bottom: 2px solid #e0e0e0;
}

header h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 700;
}

.subtitle {
    font-size: 1.1rem;
    color: #7f8c8d;
    font-weight: 300;
}

/* 上传区域样式 */
.upload-section {
    margin-bottom: 40px;
}

.upload-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.upload-box {
    background: #fff;
    border: 3px dashed #bdc3c7;
    border-radius: 15px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.upload-box:hover {
    border-color: #3498db;
    background: #f8f9fa;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(52, 152, 219, 0.1);
}

.upload-box.file-selected {
    border-color: #27ae60;
    background: #e8f5e8;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.upload-box h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.upload-box p {
    color: #7f8c8d;
    margin-bottom: 20px;
}

.upload-btn {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.upload-btn:hover {
    background: linear-gradient(45deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.file-info {
    margin-top: 15px;
    padding: 10px;
    background: #ecf0f1;
    border-radius: 8px;
    font-size: 0.9rem;
    color: #2c3e50;
    display: none;
}

.file-info.show {
    display: block;
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.primary-btn {
    background: linear-gradient(45deg, #27ae60, #229954);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 30px;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.3);
}

.primary-btn:hover:not(:disabled) {
    background: linear-gradient(45deg, #229954, #1e8449);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
}

.primary-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.secondary-btn {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 30px;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.3);
}

.secondary-btn:hover {
    background: linear-gradient(45deg, #c0392b, #a93226);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
}

/* 进度条样式 */
.progress-section {
    margin: 30px 0;
    text-align: center;
}

.progress-container {
    max-width: 500px;
    margin: 0 auto;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #ecf0f1;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #3498db, #2980b9);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* 结果区域样式 */
.results-section {
    margin-top: 40px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e0e0e0;
}

.results-header h2 {
    color: #2c3e50;
    font-size: 1.8rem;
}

.export-btn {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.export-btn:hover {
    background: linear-gradient(45deg, #e67e22, #d35400);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
}

/* 统计卡片样式 */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-5px);
}

.card-icon {
    font-size: 2.5rem;
}

.card-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.card-label {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* 表格样式 */
.table-container {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

table {
    width: 100%;
    border-collapse: collapse;
}

thead {
    background: linear-gradient(45deg, #34495e, #2c3e50);
    color: white;
}

th, td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
}

th {
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

tbody tr:hover {
    background: #f8f9fa;
}

/* 表格行状态样式 */
tbody tr.no-match {
    background: #fff5f5;
}

tbody tr.fuzzy-match {
    background: #fffbf0;
}

/* 价格差异样式 */
.price-higher {
    color: #e53e3e;
    font-weight: 600;
}

.price-lower {
    color: #38a169;
    font-weight: 600;
}

.price-equal {
    color: #718096;
    font-weight: 600;
}

/* 状态徽章样式 */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.matched {
    background: #c6f6d5;
    color: #22543d;
}

.status-badge.not_found {
    background: #fed7d7;
    color: #742a2a;
}

/* 结果筛选器样式 */
.result-filters {
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
    border: 1px solid #e9ecef;
}

.result-filters label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #495057;
    cursor: pointer;
}

.result-filters input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #3498db;
}

.result-filters select {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    background: white;
    font-size: 0.9rem;
    color: #495057;
    cursor: pointer;
}

.result-filters select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* 数据预览模态框样式 */
.preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1002;
}

.preview-content {
    background: white;
    border-radius: 15px;
    max-width: 90%;
    max-height: 90%;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.preview-header {
    background: linear-gradient(45deg, #34495e, #2c3e50);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.close-preview {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.close-preview:hover {
    background: rgba(255, 255, 255, 0.2);
}

.preview-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.preview-table {
    margin-top: 15px;
}

.preview-table-content {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
}

.preview-table-content th,
.preview-table-content td {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    text-align: left;
}

.preview-table-content th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.preview-table-content tbody tr:nth-child(even) {
    background: #f8f9fa;
}

/* 表格响应式优化 */
@media (max-width: 768px) {
    .table-container {
        overflow-x: auto;
    }

    table {
        min-width: 800px;
    }

    .result-filters {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .preview-content {
        max-width: 95%;
        max-height: 95%;
    }

    .preview-header {
        padding: 15px;
    }

    .preview-body {
        padding: 15px;
    }
}

/* 错误区域样式 */
.error-section {
    margin: 30px 0;
    text-align: center;
}

.error-container {
    background: #fff5f5;
    border: 2px solid #fed7d7;
    border-radius: 15px;
    padding: 30px;
    max-width: 500px;
    margin: 0 auto;
}

.error-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.error-content h3 {
    color: #e53e3e;
    margin-bottom: 10px;
}

.error-content p {
    color: #744210;
    margin-bottom: 20px;
}

.retry-btn {
    background: linear-gradient(45deg, #e53e3e, #c53030);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    background: linear-gradient(45deg, #c53030, #9c2626);
    transform: translateY(-2px);
}

/* 错误详情样式 */
.error-details {
    margin: 15px 0;
    font-size: 0.9rem;
}

.error-details details {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 10px;
}

.error-details summary {
    cursor: pointer;
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
}

.error-details pre {
    background: #f1f3f4;
    padding: 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    color: #333;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 10px 0 0 0;
}

/* Toast消息样式 */
.toast {
    font-family: inherit;
    font-size: 0.9rem;
    line-height: 1.4;
}

.toast-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.toast-message {
    flex: 1;
}

.toast-close {
    background: none;
    border: none;
    color: inherit;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.2s ease;
    flex-shrink: 0;
}

.toast-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-text {
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 帮助按钮样式 */
.help-section {
    margin-top: 20px;
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.help-btn, .demo-btn {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(155, 89, 182, 0.3);
}

.help-btn:hover, .demo-btn:hover {
    background: linear-gradient(45deg, #8e44ad, #7d3c98);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(155, 89, 182, 0.4);
}

.demo-btn {
    background: linear-gradient(45deg, #16a085, #138d75);
    box-shadow: 0 3px 10px rgba(22, 160, 133, 0.3);
}

.demo-btn:hover {
    background: linear-gradient(45deg, #138d75, #117a65);
    box-shadow: 0 5px 15px rgba(22, 160, 133, 0.4);
}

/* 帮助模态框样式 */
.help-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1003;
}

.help-content {
    background: white;
    border-radius: 15px;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    margin: 20px;
}

.help-header {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.help-header h3 {
    margin: 0;
    font-size: 1.4rem;
}

.close-help {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.close-help:hover {
    background: rgba(255, 255, 255, 0.2);
}

.help-body {
    padding: 25px;
    max-height: 70vh;
    overflow-y: auto;
}

.help-section {
    margin-bottom: 25px;
}

.help-section h4 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.1rem;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 5px;
}

.help-section p {
    color: #555;
    line-height: 1.6;
    margin-bottom: 10px;
}

.help-section ul {
    color: #555;
    line-height: 1.6;
    padding-left: 20px;
}

.help-section li {
    margin-bottom: 8px;
}

/* 快捷键样式 */
kbd {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 0.8rem;
    font-family: monospace;
    color: #495057;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* 状态演示样式 */
.status-demo {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-right: 10px;
}

.status-demo.exact {
    background: #c6f6d5;
    color: #22543d;
}

.status-demo.fuzzy {
    background: #fef5e7;
    color: #744210;
}

.status-demo.no-match {
    background: #fed7d7;
    color: #742a2a;
}

/* 底部样式 */
footer {
    text-align: center;
    margin-top: 50px;
    padding-top: 30px;
    border-top: 2px solid #e0e0e0;
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
        border-radius: 15px;
    }
    
    .upload-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .results-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    table {
        font-size: 0.8rem;
    }
    
    th, td {
        padding: 10px 8px;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 2rem;
    }
    
    .upload-box {
        padding: 25px 15px;
    }
    
    .upload-icon {
        font-size: 2rem;
    }
    
    .primary-btn, .secondary-btn {
        padding: 12px 20px;
        font-size: 1rem;
    }
}
