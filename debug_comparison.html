<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>比价功能调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
        }
        .upload-box {
            display: inline-block;
            margin: 10px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            text-align: center;
            min-width: 200px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .debug-log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
            white-space: pre-wrap;
        }
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .results-table th,
        .results-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .results-table th {
            background-color: #f2f2f2;
        }
        .status-matched { color: green; }
        .status-not-found { color: red; }
        .price-positive { color: red; }
        .price-negative { color: green; }
        .file-info {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>🔍 比价功能调试页面</h1>
        
        <div class="upload-section">
            <h3>📁 文件上传</h3>
            <div class="upload-box">
                <h4>销售价目表</h4>
                <input type="file" id="salesFile" accept=".xlsx,.xls">
                <div class="file-info" id="salesFileInfo"></div>
            </div>
            <div class="upload-box">
                <h4>菜篮子价格表</h4>
                <input type="file" id="basketFile" accept=".xlsx,.xls">
                <div class="file-info" id="basketFileInfo"></div>
            </div>
        </div>

        <div class="controls">
            <button class="btn" id="loadDemoBtn">📋 加载示例文件</button>
            <button class="btn" id="analyzeBtn" disabled>🔍 开始分析</button>
            <button class="btn" id="clearLogBtn">🗑️ 清空日志</button>
        </div>

        <div class="debug-log" id="debugLog">等待开始分析...</div>

        <div id="resultsSection" style="display: none;">
            <h3>📊 比价结果</h3>
            <table class="results-table" id="resultsTable">
                <thead>
                    <tr>
                        <th>商品名称</th>
                        <th>单位</th>
                        <th>销售价格</th>
                        <th>菜篮子价格</th>
                        <th>价格差异</th>
                        <th>差异百分比</th>
                        <th>匹配状态</th>
                    </tr>
                </thead>
                <tbody id="resultsTableBody">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        class DebugComparison {
            constructor() {
                this.salesData = null;
                this.basketData = null;
                this.comparisonResults = null;
                this.debugLog = document.getElementById('debugLog');
                this.bindEvents();
            }

            bindEvents() {
                document.getElementById('salesFile').addEventListener('change', (e) => this.handleSalesFile(e));
                document.getElementById('basketFile').addEventListener('change', (e) => this.handleBasketFile(e));
                document.getElementById('loadDemoBtn').addEventListener('click', () => this.loadDemoFiles());
                document.getElementById('analyzeBtn').addEventListener('click', () => this.startAnalysis());
                document.getElementById('clearLogBtn').addEventListener('click', () => this.clearLog());
            }

            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                this.debugLog.textContent += `[${timestamp}] ${message}\n`;
                this.debugLog.scrollTop = this.debugLog.scrollHeight;
                console.log(message);
            }

            clearLog() {
                this.debugLog.textContent = '';
            }

            async handleSalesFile(event) {
                const file = event.target.files[0];
                if (file) {
                    this.log(`📁 加载销售价目表: ${file.name}`);
                    try {
                        this.salesData = await this.readExcelFile(file);
                        this.log(`✅ 销售价目表加载成功，共 ${this.salesData.length} 行数据`);
                        document.getElementById('salesFileInfo').textContent = `已加载: ${file.name} (${this.salesData.length} 行)`;
                        this.checkReadyToAnalyze();
                    } catch (error) {
                        this.log(`❌ 销售价目表加载失败: ${error.message}`);
                    }
                }
            }

            async handleBasketFile(event) {
                const file = event.target.files[0];
                if (file) {
                    this.log(`📁 加载菜篮子价格表: ${file.name}`);
                    try {
                        this.basketData = await this.readExcelFile(file);
                        this.log(`✅ 菜篮子价格表加载成功，共 ${this.basketData.length} 行数据`);
                        document.getElementById('basketFileInfo').textContent = `已加载: ${file.name} (${this.basketData.length} 行)`;
                        this.checkReadyToAnalyze();
                    } catch (error) {
                        this.log(`❌ 菜篮子价格表加载失败: ${error.message}`);
                    }
                }
            }

            checkReadyToAnalyze() {
                const ready = this.salesData && this.basketData;
                document.getElementById('analyzeBtn').disabled = !ready;
                if (ready) {
                    this.log('🎯 两个文件都已加载，可以开始分析');
                }
            }

            async loadDemoFiles() {
                this.log('📋 开始加载示例文件...');
                try {
                    // 加载销售价目表
                    const salesResponse = await fetch('2025年7月销售价目表2025.xlsx');
                    if (salesResponse.ok) {
                        const salesBlob = await salesResponse.blob();
                        this.salesData = await this.readExcelFile(salesBlob);
                        this.log(`✅ 示例销售价目表加载成功，共 ${this.salesData.length} 行数据`);
                        document.getElementById('salesFileInfo').textContent = `已加载示例文件 (${this.salesData.length} 行)`;
                    }

                    // 加载菜篮子价格表
                    const basketResponse = await fetch('广东省菜篮子价格监测日报表（2025年06月01日_2025年06月26日）监测对比表.xlsx');
                    if (basketResponse.ok) {
                        const basketBlob = await basketResponse.blob();
                        this.basketData = await this.readExcelFile(basketBlob);
                        this.log(`✅ 示例菜篮子价格表加载成功，共 ${this.basketData.length} 行数据`);
                        document.getElementById('basketFileInfo').textContent = `已加载示例文件 (${this.basketData.length} 行)`;
                    }

                    this.checkReadyToAnalyze();
                } catch (error) {
                    this.log(`❌ 示例文件加载失败: ${error.message}`);
                }
            }

            async readExcelFile(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const data = new Uint8Array(e.target.result);
                            const workbook = XLSX.read(data, { type: 'array' });
                            const firstSheetName = workbook.SheetNames[0];
                            const worksheet = workbook.Sheets[firstSheetName];
                            const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                            
                            // 转换为对象格式
                            const headers = jsonData[0];
                            const rows = jsonData.slice(1).map(row => {
                                const obj = {};
                                headers.forEach((header, index) => {
                                    obj[header] = row[index];
                                });
                                return obj;
                            });
                            
                            resolve(rows);
                        } catch (error) {
                            reject(error);
                        }
                    };
                    reader.onerror = () => reject(new Error('文件读取失败'));
                    reader.readAsArrayBuffer(file);
                });
            }

            async startAnalysis() {
                this.log('🚀 开始比价分析...');
                this.clearLog();
                this.log('🚀 开始比价分析...');
                
                try {
                    // 创建比价器实例
                    const comparer = new window.PriceComparer();
                    comparer.salesData = this.salesData;
                    comparer.basketData = this.basketData;
                    
                    // 重写console.log来捕获调试信息
                    const originalLog = console.log;
                    console.log = (...args) => {
                        originalLog.apply(console, args);
                        this.log(args.join(' '));
                    };
                    
                    // 执行分析
                    await comparer.performComparison();
                    this.comparisonResults = comparer.comparisonResults;
                    
                    // 恢复console.log
                    console.log = originalLog;
                    
                    this.log('✅ 分析完成，显示结果...');
                    this.displayResults();
                    
                } catch (error) {
                    this.log(`❌ 分析失败: ${error.message}`);
                    console.error('分析错误:', error);
                }
            }

            displayResults() {
                if (!this.comparisonResults) {
                    this.log('❌ 没有分析结果可显示');
                    return;
                }

                const resultsSection = document.getElementById('resultsSection');
                const tbody = document.getElementById('resultsTableBody');
                
                tbody.innerHTML = '';
                
                this.comparisonResults.forEach(result => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${result.productName}</td>
                        <td>${result.unit}</td>
                        <td>${result.salesPrice !== null ? '¥' + result.salesPrice.toFixed(2) : '-'}</td>
                        <td>${result.basketPrice !== null ? '¥' + result.basketPrice.toFixed(2) : '-'}</td>
                        <td class="${result.priceDiff > 0 ? 'price-positive' : 'price-negative'}">
                            ${result.priceDiff !== null ? (result.priceDiff >= 0 ? '+' : '') + result.priceDiff.toFixed(2) : '-'}
                        </td>
                        <td class="${result.priceDiffPercent > 0 ? 'price-positive' : 'price-negative'}">
                            ${result.priceDiffPercent !== null ? (result.priceDiffPercent >= 0 ? '+' : '') + result.priceDiffPercent.toFixed(1) + '%' : '-'}
                        </td>
                        <td class="status-${result.status}">
                            ${result.status === 'matched' ? '✅ 已匹配' : '❌ 未找到'}
                            ${result.matchType ? ` (${result.matchType})` : ''}
                        </td>
                    `;
                    tbody.appendChild(row);
                });

                resultsSection.style.display = 'block';
                this.log(`📊 结果显示完成，共 ${this.comparisonResults.length} 个商品`);
            }
        }

        // 等待页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new DebugComparison();
        });
    </script>
    <script src="script.js"></script>
</body>
</html>
